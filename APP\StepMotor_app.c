#include "MyDefine.h"
#include "StepMotor_app.h"
#include <math.h>
#include <stdlib.h>

/* ========== 编码器全局变量定义 ========== */
uint8_t motor_x_rx_buffer[MOTOR_RX_BUFFER_SIZE] = {0};  // X轴编码器接收缓冲区
uint8_t motor_y_rx_buffer[MOTOR_RX_BUFFER_SIZE] = {0};  // Y轴编码器接收缓冲区
uint8_t motor_x_rx_state = 0;                           // X轴接收状态机状态
uint8_t motor_y_rx_state = 0;                           // Y轴接收状态机状态
uint8_t motor_x_rx_counter = 0;                         // X轴接收计数器
uint8_t motor_y_rx_counter = 0;                         // Y轴接收计数器
uint8_t motor_x_ready = 0;                              // X轴电机到位标志
uint8_t motor_y_ready = 0;                              // Y轴电机到位标志
uint8_t stop_flag_car = 0;                              // 系统状态标志
uint8_t motor_x_rx_byte = 0;                            // X轴单字节接收缓冲
uint8_t motor_y_rx_byte = 0;                            // Y轴单字节接收缓冲

/* ========== 脉冲计数全局变量定义 ========== */
int32_t motor_x_pulse_count = 0;                        // X轴当前脉冲计数
int32_t motor_y_pulse_count = 0;                        // Y轴当前脉冲计数
int32_t motor_x_target_pulses = 0;                      // X轴目标脉冲数
int32_t motor_y_target_pulses = 0;                      // Y轴目标脉冲数

/* ========== Y轴回零功能全局变量定义 ========== */
static MotorHomeStatus_t motor_y_home_status = MOTOR_HOME_IDLE;  // Y轴回零状态
static uint32_t motor_y_home_start_time = 0;                    // Y轴回零开始时间
void StepMotor_Move_Pulses_Speed(int32_t x_pulses, int32_t y_pulses, uint16_t speed_rpm)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0) {
        x_dir = 0;               /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    } else {
        x_dir = 1;               /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0) {
        y_dir = 0;               /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    } else {
        y_dir = 1;               /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 更新目标脉冲数 */
    motor_x_target_pulses = x_pulses;
    motor_y_target_pulses = y_pulses;

    /* 使用指定速度运行位置模式。保持加速度为宏定义 MOTOR_ACCEL，
       raF=false 表示相对运动，snF 使用全局宏 MOTOR_SYNC_FLAG。 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, speed_rpm, MOTOR_ACCEL,
                       y_pulse_abs, false, MOTOR_SYNC_FLAG);
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, speed_rpm, MOTOR_ACCEL,
                       x_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 更新当前脉冲计数 */
    motor_x_pulse_count += x_pulses;
    motor_y_pulse_count += y_pulses;
}

/**
 * @brief 控制XY轴电机转动固定的脉冲数
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 更新目标脉冲数 */
    motor_x_target_pulses = x_pulses;
    motor_y_target_pulses = y_pulses;

    /* 控制Y轴电机进行脉冲运动 - 这里是问题所在，Y和X反了 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, y_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 控制X轴电机进行脉冲运动 - 这里是问题所在，X和Y反了 */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, x_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 更新当前脉冲计数 */
    motor_x_pulse_count += x_pulses;
    motor_y_pulse_count += y_pulses;
}
/**
 * @brief �����ʼ������
 */
void StepMotor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    StepMotor_Stop();

    /* ========== 编码器初始化 ========== */
    StepMotor_Clear_Ready_Flags();                          // 清除编码器到位标志
    HAL_UART_Receive_IT(&MOTOR_X_UART, &motor_x_rx_byte, 1); // 启动X轴编码器中断接收
    HAL_UART_Receive_IT(&MOTOR_Y_UART, &motor_y_rx_byte, 1); // 启动Y轴编码器中断接收
    my_printf(&huart1, "StepMotor+Encoder Init OK\r\n");

    /* ========== 可选的Y轴自动回零功能 ========== */
#ifdef MOTOR_AUTO_HOME_ENABLE
    my_printf(&huart1, "开始Y轴自动回零...\r\n");
    uint8_t home_result = StepMotor_Y_Home(MOTOR_HOME_MODE_ENCODER, MOTOR_HOME_TIMEOUT_DEFAULT);
    if (home_result == 1) {
        my_printf(&huart1, "Y轴自动回零成功!\r\n");
    } else {
        my_printf(&huart1, "Y轴自动回零失败: 错误代码=%d\r\n", home_result);
        my_printf(&huart1, "系统将继续运行，可稍后手动回零\r\n");
    }
#endif
}

/**
 * @brief Y轴零点初始化设置（在Y轴已手动调整到理想位置后调用）
 */
void StepMotor_Y_Init_Zero(void)
{
    my_printf(&huart1, "开始设置Y轴零点...\r\n");

    // 等待系统稳定
    HAL_Delay(500);

    // 设置当前位置为零点
    uint8_t result = StepMotor_Y_Set_Zero_Position();

    if (result == 1) {
        my_printf(&huart1, "Y轴零点设置成功！当前位置已设为原点\r\n");
        my_printf(&huart1, "Y轴脉冲计数已重置为: %ld\r\n", motor_y_pulse_count);
    } else {
        my_printf(&huart1, "Y轴零点设置失败，错误代码: %d\r\n", result);
    }
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ֹͣ���е��
 */
void StepMotor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

/* ========== 编码器数据处理函数 ========== */

/**
 * @brief X轴电机编码器数据处理函数
 * @param com_data 接收到的单字节数据
 */
void Motor_X_Receive_Data(uint8_t com_data)
{
    uint8_t i;

    // 状态0：等待起始字节(0x01)
    if (motor_x_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
    {
        motor_x_rx_state = 1;                               // 进入数据接收状态
        motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // 存储起始字节
    }
    // 状态1：接收数据字节
    else if (motor_x_rx_state == 1)
    {
        motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // 存储数据字节

        // 如果缓冲区满或接收到结束字节0x6B，进入解析状态
        if (motor_x_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
        {
            motor_x_rx_state = 2;
        }
    }

    // 状态2：解析接收到的数据
    if (motor_x_rx_state == 2)
    {
        // 检查最后一个字节是否为0x6B，验证数据完整性
        if (motor_x_rx_buffer[motor_x_rx_counter - 1] == MOTOR_END_BYTE)
        {
            // 检查0x01起始字节和到位标识字节0xFD 0x9F
            if (motor_x_rx_buffer[0] == MOTOR_START_BYTE_01 &&
                motor_x_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
                motor_x_rx_buffer[2] == MOTOR_READY_FLAG_2)
            {
                motor_x_ready = 1; // X轴电机到位标志设置
                my_printf(&huart1, "X_Motor_Ready\r\n");
            }

            // 清空接收缓冲区
            for (i = 0; i < motor_x_rx_counter; i++)
            {
                motor_x_rx_buffer[i] = 0x00;
            }
        }
        else // 最后字节不是0x6B，数据帧错误
        {
            stop_flag_car = 2; // 设置通信错误标志
            my_printf(&huart1, "X_Comm_Error\r\n");
            // 清空接收缓冲区
            for (i = 0; i < motor_x_rx_counter; i++)
            {
                motor_x_rx_buffer[i] = 0x00;
            }
        }

        // 重置状态机和计数器
        motor_x_rx_state = 0;
        motor_x_rx_counter = 0;
    }
}
/**
 * @brief Y轴电机编码器数据处理函数
 * @param com_data 接收到的单字节数据
 */
void Motor_Y_Receive_Data(uint8_t com_data)
{
    uint8_t i;

    // 状态0：等待起始字节(0x01)
    if (motor_y_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
    {
        motor_y_rx_state = 1;                               // 进入数据接收状态
        motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // 存储起始字节
    }
    // 状态1：接收数据字节
    else if (motor_y_rx_state == 1)
    {
        motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // 存储数据字节

        // 如果缓冲区满或接收到结束字节0x6B，进入解析状态
        if (motor_y_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
        {
            motor_y_rx_state = 2;
        }
    }

    // 状态2：解析接收到的数据
    if (motor_y_rx_state == 2)
    {
        // 检查最后一个字节是否为0x6B，验证数据完整性
        if (motor_y_rx_buffer[motor_y_rx_counter - 1] == MOTOR_END_BYTE)
        {
            // 检查0x01起始字节和到位标识字节0xFD 0x9F
            if (motor_y_rx_buffer[0] == MOTOR_START_BYTE_01 &&
                motor_y_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
                motor_y_rx_buffer[2] == MOTOR_READY_FLAG_2)
            {
                motor_y_ready = 1; // Y轴电机到位标志设置
                my_printf(&huart1, "Y_Motor_Ready\r\n");
            }

            // 清空接收缓冲区
            for (i = 0; i < motor_y_rx_counter; i++)
            {
                motor_y_rx_buffer[i] = 0x00;
            }
        }
        else // 最后字节不是0x6B，数据帧错误
        {
            stop_flag_car = 2; // 设置通信错误标志
            my_printf(&huart1, "Y_Comm_Error\r\n");
            // 清空接收缓冲区
            for (i = 0; i < motor_y_rx_counter; i++)
            {
                motor_y_rx_buffer[i] = 0x00;
            }
        }

        // 重置状态机和计数器
        motor_y_rx_state = 0;
        motor_y_rx_counter = 0;
    }
}
/* ========== 编码器状态检测API函数 ========== */

/**
 * @brief 检查电机是否准备好进行下一步操作
 * @return 0: 电机运动中, 1: 电机已到位, 2: 通信错误
 */
uint8_t StepMotor_Check_Ready(void)
{
    // 检查通信错误
    if (stop_flag_car == 2)
    {
        my_printf(&huart1, "电机通信错误!\r\n");
        return 2; // 通信错误
    }

    // 检查两个电机是否都到位
    if (motor_x_ready && motor_y_ready)
    {
        my_printf(&huart1, "X轴和Y轴电机都已到位，可以进行下一步操作!\r\n");
        return 1; // 电机已到位
    }

    return 0; // 电机运动中
}

/**
 * @brief 清除电机到位标志，为下一次运动做准备
 */
void StepMotor_Clear_Ready_Flags(void)
{
    motor_x_ready = 0;
    motor_y_ready = 0;
    stop_flag_car = 0;
    my_printf(&huart1, "电机到位标志已清除，准备下一次运动!\r\n");
}

/**
 * @brief 获取指定电机的到位状态
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机)
 * @return 1: 电机到位, 0: 电机运动中
 */
uint8_t StepMotor_Get_Motor_Status(uint8_t motor_id)
{
    if (motor_id == 1)
        return motor_x_ready;
    else if (motor_id == 2)
        return motor_y_ready;
    else
        return 0;
}

/**
 * @brief 独立的编码器初始化函数
 */
void StepMotor_Init_Encoder(void)
{
    StepMotor_Clear_Ready_Flags();                          // 清除编码器到位标志
    HAL_UART_Receive_IT(&MOTOR_X_UART, &motor_x_rx_byte, 1); // 启动X轴编码器中断接收
    HAL_UART_Receive_IT(&MOTOR_Y_UART, &motor_y_rx_byte, 1); // 启动Y轴编码器中断接收
    my_printf(&huart1, "编码器初始化完成!\r\n");
}
/**
 * @brief 控制XY轴电机转动固定脉冲数并等待到位（带超时保护）
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 * @param timeout_ms 超时时间（毫秒）
 * @return 0: 超时, 1: 电机已到位, 2: 通信错误
 */
uint8_t StepMotor_Move_Pulses_Wait(int32_t x_pulses, int32_t y_pulses, uint32_t timeout_ms)
{
    // 清除之前的到位标志
    StepMotor_Clear_Ready_Flags();

    // 开始电机运动
    StepMotor_Move_Pulses(x_pulses, y_pulses);
    my_printf(&huart1, "开始电机运动: X=%ld, Y=%ld 脉冲\r\n", x_pulses, y_pulses);

    // 等待电机到位或超时
    uint32_t start_time = HAL_GetTick();
    while (HAL_GetTick() - start_time < timeout_ms)
    {
        uint8_t status = StepMotor_Check_Ready();
        if (status == 1)
        {
            my_printf(&huart1, "电机运动完成，已到位!\r\n");
            return 1; // 电机已到位
        }
        else if (status == 2)
        {
            my_printf(&huart1, "电机通信错误!\r\n");
            return 2; // 通信错误
        }
        HAL_Delay(10); // 短暂延时，避免过度占用CPU
    }

    my_printf(&huart1, "电机运动超时 (%lu ms)!\r\n", timeout_ms);
    return 0; // 超时
}

/**
 * @brief 控制XY轴电机转动固定脉冲数（非阻塞版本，立即返回）
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses_Async(int32_t x_pulses, int32_t y_pulses)
{
    // 清除之前的到位标志
    StepMotor_Clear_Ready_Flags();

    // 开始电机运动
    StepMotor_Move_Pulses(x_pulses, y_pulses);
    my_printf(&huart1, "Async Move: X=%ld Y=%ld\r\n", x_pulses, y_pulses);
}

/* ========== 脉冲计数管理API函数 ========== */

/**
 * @brief 获取指定电机的当前脉冲计数
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机)
 * @return 当前脉冲计数值
 */
int32_t StepMotor_Get_Pulse_Count(uint8_t motor_id)
{
    if (motor_id == 1) {
        return motor_x_pulse_count;
    } else if (motor_id == 2) {
        return motor_y_pulse_count;
    }
    return 0;
}

/**
 * @brief 获取指定电机的目标脉冲数
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机)
 * @return 目标脉冲数
 */
int32_t StepMotor_Get_Target_Pulses(uint8_t motor_id)
{
    if (motor_id == 1) {
        return motor_x_target_pulses;
    } else if (motor_id == 2) {
        return motor_y_target_pulses;
    }
    return 0;
}

/**
 * @brief 重置指定电机的脉冲计数
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机, 0: 重置所有)
 */
void StepMotor_Reset_Pulse_Count(uint8_t motor_id)
{
    if (motor_id == 1 || motor_id == 0) {
        motor_x_pulse_count = 0;
        motor_x_target_pulses = 0;
    }
    if (motor_id == 2 || motor_id == 0) {
        motor_y_pulse_count = 0;
        motor_y_target_pulses = 0;
    }
    my_printf(&huart1, "Pulse Reset: Motor_%d\r\n", motor_id);
}

/**
 * @brief 获取所有电机的脉冲信息
 * @param x_current 返回X轴当前脉冲计数
 * @param y_current 返回Y轴当前脉冲计数
 * @param x_target 返回X轴目标脉冲数
 * @param y_target 返回Y轴目标脉冲数
 */
void StepMotor_Get_All_Pulse_Info(int32_t *x_current, int32_t *y_current,
                                  int32_t *x_target, int32_t *y_target)
{
    if (x_current) *x_current = motor_x_pulse_count;
    if (y_current) *y_current = motor_y_pulse_count;
    if (x_target) *x_target = motor_x_target_pulses;
    if (y_target) *y_target = motor_y_target_pulses;
}

/**
 * @brief 打印当前脉冲状态信息
 */
void StepMotor_Print_Pulse_Status(void)
{
    my_printf(&huart1, "Pulse Status:\r\n");
    my_printf(&huart1, "X: Current=%ld Target=%ld\r\n", motor_x_pulse_count, motor_x_target_pulses);
    my_printf(&huart1, "Y: Current=%ld Target=%ld\r\n", motor_y_pulse_count, motor_y_target_pulses);
}

/* ========== Y轴回零功能实现 ========== */

/**
 * @brief Y轴电机回零（阻塞版本）
 * @param home_mode 回零模式 (0-单圈脉冲寻找原点, 1-单圈编码器寻找原点, 2-单圈编码器碰撞寻找原点, 3-单圈编码器回退寻找原点)
 * @param timeout_ms 超时时间（毫秒）
 * @return 0: 超时, 1: 回零成功, 2: 通信错误, 3: 参数错误
 */
uint8_t StepMotor_Y_Home(uint8_t home_mode, uint32_t timeout_ms)
{
    // 参数验证
    if (home_mode > 3)
    {
        my_printf(&huart1, "Y轴回零参数错误: mode=%d (有效范围0-3)\r\n", home_mode);
        return 3; // 参数错误
    }

    if (timeout_ms == 0)
    {
        timeout_ms = MOTOR_HOME_TIMEOUT_DEFAULT; // 使用默认超时时间
    }

    // 更新回零状态
    motor_y_home_status = MOTOR_HOME_RUNNING;
    motor_y_home_start_time = HAL_GetTick();

    // 清除之前的到位标志
    StepMotor_Clear_Ready_Flags();

    // 触发Y轴回零操作
    Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, home_mode, MOTOR_SYNC_FLAG);
    my_printf(&huart1, "开始Y轴回零: mode=%d, timeout=%lu ms\r\n", home_mode, timeout_ms);

    // 等待回零完成或超时
    uint32_t start_time = HAL_GetTick();
    while (HAL_GetTick() - start_time < timeout_ms)
    {
        uint8_t status = StepMotor_Check_Ready();
        if (status == 1)
        {
            motor_y_home_status = MOTOR_HOME_SUCCESS;
            my_printf(&huart1, "Y轴回零成功!\r\n");
            return 1; // 回零成功
        }
        else if (status == 2)
        {
            motor_y_home_status = MOTOR_HOME_ERROR;
            my_printf(&huart1, "Y轴回零通信错误!\r\n");
            return 2; // 通信错误
        }
        HAL_Delay(10); // 短暂延时，避免过度占用CPU
    }

    motor_y_home_status = MOTOR_HOME_TIMEOUT;
    my_printf(&huart1, "Y轴回零超时 (%lu ms)!\r\n", timeout_ms);
    return 0; // 超时
}

/**
 * @brief Y轴电机回零（非阻塞版本）
 * @param home_mode 回零模式 (0-单圈脉冲寻找原点, 1-单圈编码器寻找原点, 2-单圈编码器碰撞寻找原点, 3-单圈编码器回退寻找原点)
 * @return 0: 启动失败, 1: 成功启动回零, 3: 参数错误
 */
uint8_t StepMotor_Y_Home_Async(uint8_t home_mode)
{
    // 参数验证
    if (home_mode > 3)
    {
        my_printf(&huart1, "Y轴异步回零参数错误: mode=%d (有效范围0-3)\r\n", home_mode);
        return 3; // 参数错误
    }

    // 检查是否已在回零中
    if (motor_y_home_status == MOTOR_HOME_RUNNING)
    {
        my_printf(&huart1, "Y轴回零已在进行中，请等待完成\r\n");
        return 0; // 启动失败
    }

    // 更新回零状态
    motor_y_home_status = MOTOR_HOME_RUNNING;
    motor_y_home_start_time = HAL_GetTick();

    // 清除之前的到位标志
    StepMotor_Clear_Ready_Flags();

    // 触发Y轴回零操作
    Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, home_mode, MOTOR_SYNC_FLAG);
    my_printf(&huart1, "Y轴异步回零启动: mode=%d\r\n", home_mode);

    return 1; // 成功启动回零
}

/**
 * @brief 检查Y轴回零状态
 * @return MotorHomeStatus_t枚举值 (0-未开始, 1-进行中, 2-成功, 3-超时, 4-错误)
 */
uint8_t StepMotor_Y_Check_Home_Status(void)
{
    // 如果不在回零状态，直接返回当前状态
    if (motor_y_home_status != MOTOR_HOME_RUNNING)
    {
        return motor_y_home_status;
    }

    // 检查是否超时（使用默认超时时间）
    uint32_t elapsed_time = HAL_GetTick() - motor_y_home_start_time;
    if (elapsed_time > MOTOR_HOME_TIMEOUT_DEFAULT)
    {
        motor_y_home_status = MOTOR_HOME_TIMEOUT;
        my_printf(&huart1, "Y轴回零超时检测: %lu ms\r\n", elapsed_time);
        return MOTOR_HOME_TIMEOUT;
    }

    // 检查电机状态
    uint8_t motor_status = StepMotor_Check_Ready();
    if (motor_status == 1)
    {
        motor_y_home_status = MOTOR_HOME_SUCCESS;
        my_printf(&huart1, "Y轴回零完成检测\r\n");
        return MOTOR_HOME_SUCCESS;
    }
    else if (motor_status == 2)
    {
        motor_y_home_status = MOTOR_HOME_ERROR;
        my_printf(&huart1, "Y轴回零通信错误检测\r\n");
        return MOTOR_HOME_ERROR;
    }

    // 仍在进行中
    return MOTOR_HOME_RUNNING;
}

/**
 * @brief 设置Y轴当前位置为零点
 * @return 0: 设置失败, 1: 设置成功, 2: 通信错误
 */
uint8_t StepMotor_Y_Set_Zero_Position(void)
{
    // 设置当前位置为原点
    Emm_V5_Origin_Set_O(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);

    // 等待命令执行
    HAL_Delay(100);

    // 重置脉冲计数器
    motor_y_pulse_count = 0;
    motor_y_target_pulses = 0;

    my_printf(&huart1, "Y轴零点已设置为当前位置\r\n");
    return 1; // 设置成功
}

/**
 * @brief 设置Y轴零点偏移量
 * @param offset_pulses 偏移脉冲数（正数向前偏移，负数向后偏移）
 * @return 0: 设置失败, 1: 设置成功, 2: 通信错误, 3: 参数错误
 */
uint8_t StepMotor_Y_Set_Zero_Offset(int32_t offset_pulses)
{
    // 参数范围检查（限制在合理范围内）
    if (abs(offset_pulses) > 10000) {
        my_printf(&huart1, "Y轴零点偏移参数错误: %ld (范围±10000)\r\n", offset_pulses);
        return 3; // 参数错误
    }

    // 设置零点偏移参数
    // trigger_mode: 0=上升沿, 1=下降沿, 2=上升沿+下降沿
    // edge_mode: 0=立即停止, 1=减速停止
    uint8_t trigger_mode = 0;  // 上升沿触发
    uint8_t edge_mode = 1;     // 减速停止

    Emm_V5_Origin_Modify_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR,
                               trigger_mode, edge_mode,
                               (uint32_t)abs(offset_pulses),
                               MOTOR_SYNC_FLAG);

    // 等待命令执行
    HAL_Delay(100);

    my_printf(&huart1, "Y轴零点偏移已设置: %ld 脉冲\r\n", offset_pulses);
    return 1; // 设置成功
}


