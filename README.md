# Car_Xifeng_F4 - STM32F4步进电机控制系统

## 项目概述

基于STM32F407VETx微控制器的智能步进电机控制系统，集成了激光追踪、PID控制、轨迹规划等先进功能。系统采用分层架构设计，支持双轴步进电机精确控制。

## 主要特性

### 🎯 核心功能
- **双轴步进电机控制**: 支持X轴和Y轴独立控制
- **激光追踪系统**: 实时激光目标跟踪和自动追踪
- **PID控制算法**: 高精度位置和速度控制
- **轨迹规划**: 智能路径规划和平滑运动控制
- **编码器反馈**: 实时位置反馈和状态监控

### ⚡ **Y轴自动回零功能** (新增)
- **多种回零模式**: 支持4种不同的回零策略
- **自动上电回零**: 可选的系统启动自动回零
- **超时保护**: 内置30秒超时保护机制
- **状态反馈**: 完整的回零状态跟踪
- **阻塞/非阻塞**: 支持两种调用方式
- **向后兼容**: 不影响现有代码运行

### 🔧 硬件支持
- **主控芯片**: STM32F407VETx
- **步进电机**: Emm_V5协议驱动器
- **通信接口**: 多路UART通信
- **显示系统**: OLED显示屏
- **传感器**: 编码器、陀螺仪、激光传感器

## 系统架构

```
应用层 (APP)
├── StepMotor_app.h/c          # 步进电机应用层封装
├── laser_tracking_integrated.c # 激光追踪系统
├── path_planner.c             # 路径规划器
├── pid_app.c                  # PID控制应用
└── scheduler.c                # 任务调度器

驱动层 (Components)
├── StepMotor/Emm_V5.h/c       # 步进电机底层驱动
├── Hwt101/                    # 陀螺仪驱动
├── Encoder/                   # 编码器驱动
├── PID/                       # PID算法库
└── 其他外设驱动

HAL层
└── STM32F4xx_HAL_Driver       # STM32 HAL库
```

## 快速开始

### 1. 环境配置
- **开发环境**: Keil MDK-ARM 5.39+
- **编译器**: ARM Compiler v6
- **调试器**: ST-Link
- **目标芯片**: STM32F407VETx

### 2. 编译和烧录
```bash
# 使用Keil MDK打开项目文件
Car_Xifeng_F4.uvprojx

# 编译项目
Project -> Build Target (F7)

# 烧录到目标板
Flash -> Download (F8)
```

### 3. Y轴回零功能使用

#### 启用自动回零
在 `APP/StepMotor_app.h` 中取消注释：
```c
#define MOTOR_AUTO_HOME_ENABLE           // 启用上电自动回零
```

#### 手动回零示例
```c
#include "StepMotor_app.h"

// 阻塞版本回零
uint8_t result = StepMotor_Y_Home(MOTOR_HOME_MODE_ENCODER, 30000);
if (result == 1) {
    printf("Y轴回零成功！\n");
}

// 非阻塞版本回零
StepMotor_Y_Home_Async(MOTOR_HOME_MODE_ENCODER);
while (StepMotor_Y_Check_Home_Status() == MOTOR_HOME_RUNNING) {
    // 执行其他任务
    HAL_Delay(100);
}
```

## 硬件连接

### 步进电机连接
- **X轴电机**: UART2 (huart2), 地址 0x01
- **Y轴电机**: UART4 (huart4), 地址 0x01
- **通信协议**: Emm_V5, 波特率 9600

### 其他外设
- **调试串口**: UART1 (huart1) - 115200波特率
- **OLED显示**: I2C接口
- **编码器**: 中断接收模式
- **陀螺仪**: UART6 (huart6)

## 功能模块说明

### 步进电机控制
- **基础控制**: 速度控制、位置控制、停止控制
- **高级功能**: 轨迹插值、平滑加减速
- **状态监控**: 实时位置反馈、到位检测
- **回零功能**: 多模式自动回零系统

### 激光追踪系统
- **目标检测**: 红绿激光点识别
- **追踪模式**: PID控制、轨迹插值、目标丢失处理
- **性能优化**: 自适应速度控制、误差阈值管理

### PID控制系统
- **双轴独立**: X轴和Y轴独立PID参数
- **参数调节**: 实时PID参数调整
- **性能监控**: 误差分析、输出限制

## 配置选项

### 电机参数配置
```c
#define MOTOR_MAX_SPEED     3            // 最大转速(RPM)
#define MOTOR_ACCEL         0            // 加速度(0表示直接启动)
#define MOTOR_MAX_ANGLE     50           // 最大角度限制(±50°)
```

### 回零功能配置
```c
#define MOTOR_HOME_MODE_ENCODER    1     // 推荐的回零模式
#define MOTOR_HOME_TIMEOUT_DEFAULT 30000 // 默认超时时间(ms)
// #define MOTOR_AUTO_HOME_ENABLE        // 自动回零功能(默认禁用)
```

### 追踪系统配置
```c
#define TRACKING_UPDATE_INTERVAL    20   // 追踪更新间隔(ms)
#define TRAJECTORY_ENABLE_THRESHOLD 10   // 轨迹插值启用阈值
#define TRACKING_SPEED_MAX         90    // 最大追踪速度(%)
```

## 文档资源

### 技术文档
- [Y轴回零功能详细指南](docs/development/Y_Axis_Homing_Guide.md)
- [HAL编码器库架构文档](docs/architecture/Architecture_HAL_Encoder_Library_v1.0.md)
- [产品需求文档](docs/prd/PRD_HAL_Encoder_Library_v1.0.md)
- [脉冲读取使用指南](docs/脉冲读取使用指南.md)

### API参考
- **StepMotor_app.h**: 步进电机应用层API
- **Emm_V5.h**: 底层驱动API
- **PID_app.h**: PID控制API
- **laser_tracking.h**: 激光追踪API

## 故障排除

### 常见问题
1. **电机不响应**: 检查串口连接和波特率设置
2. **回零失败**: 确认原点传感器工作正常
3. **追踪不准确**: 调整PID参数和追踪阈值
4. **编译错误**: 确认HAL库版本和路径配置

### 调试方法
- 使用串口1输出的调试信息
- 通过OLED显示屏查看系统状态
- 利用Keil调试器进行单步调试

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.2 | 2024 | 新增Y轴自动回零功能 |
| v1.1 | 2024 | 完善激光追踪和PID控制 |
| v1.0 | 2024 | 初始版本，基础电机控制 |

## 开发团队

**米醋电子工作室**
- 系统架构设计
- 算法优化实现
- 文档编写维护

## 许可证

本项目版权归米醋电子工作室所有。

---

## 快速链接

- 📖 [完整技术文档](docs/development/Y_Axis_Homing_Guide.md)
- 🔧 [硬件连接指南](#硬件连接)
- 🚀 [快速开始指南](#快速开始)
- ❓ [故障排除](#故障排除)

**最后更新**: 2024年 | **当前版本**: v1.2
